// Mocks generated by <PERSON>ckito 5.4.4 from annotations
// in testflutter/test/blocs/learning_step_completion_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:testflutter/models/chess_models.dart' as _i5;
import 'package:testflutter/models/learning_models.dart' as _i2;
import 'package:testflutter/services/learning_service.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLearningLesson_0 extends _i1.SmartFake
    implements _i2.LearningLesson {
  _FakeLearningLesson_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLearningStep_1 extends _i1.SmartFake implements _i2.LearningStep {
  _FakeLearningStep_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [LearningService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLearningService extends _i1.Mock implements _i3.LearningService {
  MockLearningService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.LearningLesson>> getAvailableLessons() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAvailableLessons,
          [],
        ),
        returnValue:
            _i4.Future<List<_i2.LearningLesson>>.value(<_i2.LearningLesson>[]),
      ) as _i4.Future<List<_i2.LearningLesson>>);

  @override
  _i4.Future<_i2.LearningLesson?> getLessonById(String? lessonId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLessonById,
          [lessonId],
        ),
        returnValue: _i4.Future<_i2.LearningLesson?>.value(),
      ) as _i4.Future<_i2.LearningLesson?>);

  @override
  _i4.Future<_i2.LearningLesson?> getLessonByMode(_i2.LearningMode? mode) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLessonByMode,
          [mode],
        ),
        returnValue: _i4.Future<_i2.LearningLesson?>.value(),
      ) as _i4.Future<_i2.LearningLesson?>);

  @override
  _i4.Future<void> saveProgress(_i2.LearningLesson? lesson) =>
      (super.noSuchMethod(
        Invocation.method(
          #saveProgress,
          [lesson],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i2.LearningLesson?> loadProgress(String? lessonId) =>
      (super.noSuchMethod(
        Invocation.method(
          #loadProgress,
          [lessonId],
        ),
        returnValue: _i4.Future<_i2.LearningLesson?>.value(),
      ) as _i4.Future<_i2.LearningLesson?>);

  @override
  _i4.Future<Map<String, dynamic>> getLearningStats() => (super.noSuchMethod(
        Invocation.method(
          #getLearningStats,
          [],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<void> resetLessonProgress(String? lessonId) => (super.noSuchMethod(
        Invocation.method(
          #resetLessonProgress,
          [lessonId],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<_i2.LearningLesson?> getRecommendedNextLesson(
          List<_i2.LearningLesson>? completedLessons) =>
      (super.noSuchMethod(
        Invocation.method(
          #getRecommendedNextLesson,
          [completedLessons],
        ),
        returnValue: _i4.Future<_i2.LearningLesson?>.value(),
      ) as _i4.Future<_i2.LearningLesson?>);

  @override
  bool validateMove(
    _i5.ChessMove? move,
    _i2.LearningStep? step,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateMove,
          [
            move,
            step,
          ],
        ),
        returnValue: false,
      ) as bool);

  @override
  String? getStepHint(_i2.LearningStep? step) =>
      (super.noSuchMethod(Invocation.method(
        #getStepHint,
        [step],
      )) as String?);

  @override
  _i2.LearningLesson createCustomLesson({
    required String? title,
    required String? description,
    required _i2.LearningMode? mode,
    required List<_i2.LearningStep>? steps,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCustomLesson,
          [],
          {
            #title: title,
            #description: description,
            #mode: mode,
            #steps: steps,
          },
        ),
        returnValue: _FakeLearningLesson_0(
          this,
          Invocation.method(
            #createCustomLesson,
            [],
            {
              #title: title,
              #description: description,
              #mode: mode,
              #steps: steps,
            },
          ),
        ),
      ) as _i2.LearningLesson);

  @override
  _i2.LearningStep createCustomStep({
    required String? title,
    required String? description,
    required _i2.StepType? type,
    List<String>? instructions,
    List<List<_i5.ChessPiece?>>? boardState,
    List<_i5.Position>? highlightPositions,
    List<_i5.ChessMove>? requiredMoves,
    List<_i5.ChessMove>? demonstrationMoves,
    String? successMessage,
    String? failureMessage,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #createCustomStep,
          [],
          {
            #title: title,
            #description: description,
            #type: type,
            #instructions: instructions,
            #boardState: boardState,
            #highlightPositions: highlightPositions,
            #requiredMoves: requiredMoves,
            #demonstrationMoves: demonstrationMoves,
            #successMessage: successMessage,
            #failureMessage: failureMessage,
            #metadata: metadata,
          },
        ),
        returnValue: _FakeLearningStep_1(
          this,
          Invocation.method(
            #createCustomStep,
            [],
            {
              #title: title,
              #description: description,
              #type: type,
              #instructions: instructions,
              #boardState: boardState,
              #highlightPositions: highlightPositions,
              #requiredMoves: requiredMoves,
              #demonstrationMoves: demonstrationMoves,
              #successMessage: successMessage,
              #failureMessage: failureMessage,
              #metadata: metadata,
            },
          ),
        ),
      ) as _i2.LearningStep);
}
