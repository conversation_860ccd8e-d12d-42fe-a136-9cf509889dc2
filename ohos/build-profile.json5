{"app": {"signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"storeFile": "/Users/<USER>/keys/app.p12", "storePassword": "00000019334770EA6A74D6DBB8783F793E6A99F237664D1AB9CE46298DDC57B8BE39A80C4AB5DB1E6E", "keyAlias": "app", "keyPassword": "00000019C9D4714235143C12E7FAA422FD818BFAC51A35FB17916F0384D8D1C7A4E2BF1728A9AB2021", "signAlg": "SHA256withECDSA", "profile": "/Users/<USER>/keys/appRelease.p7b", "certpath": "/Users/<USER>/keys/app-publish.cer"}}, {"name": "debug", "type": "HarmonyOS", "material": {"certpath": "/Users/<USER>/.ohos/config/debug_ohos_bZOkeL2bVkNn7fO6r0_a0-1IKNfI4xShfW7cdYcP_lM=.cer", "keyAlias": "debugKey", "keyPassword": "0000001B6BCC6DD284B93CB3D32177C3BB978D0CAE24E432E2BDC25326C509D0F00D4C4B6A8413B7BD7187", "profile": "/Users/<USER>/.ohos/config/debug_ohos_bZOkeL2bVkNn7fO6r0_a0-1IKNfI4xShfW7cdYcP_lM=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/.ohos/config/debug_ohos_bZOkeL2bVkNn7fO6r0_a0-1IKNfI4xShfW7cdYcP_lM=.p12", "storePassword": "0000001B1C78B33A3EE0E0421F47D1DBDA2A8A72C7DEB5ACB129C08274D67227828BAAED07DD891C2A45B5"}}], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.1(13)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}, "nativeCompiler": "BiSheng"}}, {"name": "debug", "signingConfig": "debug", "compatibleSdkVersion": "5.0.1(13)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"caseSensitiveCheck": true, "useNormalizedOHMUrl": true}, "nativeCompiler": "BiSheng"}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default", "debug"]}]}, {"name": "audioplayers_ohos", "srcPath": "../../code/opensource/AI/audioplayers/packages/audioplayers_ohos/ohos", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}